/* Reset and Base Styles */
:root {
    --primary-color: #14213d;
    --secondary-color: #fca311;
    --text-color: #333;
    --light-color: #f8f9fa;
    --dark-color: #0d1b2a;
    --gray-color: #e5e5e5;
    --body-font: 'Raleway', sans-serif;
    --heading-font: 'Playfair Display', serif;
    --transition: all 0.3s ease;
    --shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    --border-radius: 4px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    color: var(--text-color);
    line-height: 1.6;
    font-family: var(--body-font);
    font-weight: 400;
    background-color: var(--light-color);
}

img {
    max-width: 100%;
    height: auto;
}

a {
    text-decoration: none;
    color: inherit;
    transition: var(--transition);
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--heading-font);
    font-weight: 600;
    color: var(--primary-color);
    line-height: 1.3;
}

/* Layout */
.container {
    width: 100%;
    max-width: 1200px;
    margin: -30px auto;
    padding: 0 20px;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col {
    padding: 0 15px;
    flex: 1;
}

/* Header Styles */
header {
    background-color: rgba(255, 255, 255, 0.95);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    position: fixed;
    width: 100%;
    z-index: 1000;
    top: 20px;
    transition: top 0.4s ease, background-color 0.4s ease;
    border-radius: var(--border-radius);
}

header.scrolled {
    top: 0;
    background-color: var(--secondary-color);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
}

.logo {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
    display: flex;
    align-items: center;
}

.logo img {
    height: 60px;
    width: auto;
    transition: var(--transition);
}

.logo span {
    color: var(--secondary-color);
}

header.scrolled .logo span {
    color: var(--primary-color);
}

nav ul {
    display: flex;
    list-style: none;
    align-items: center;
}

nav ul li {
    margin-left: 35px;
    position: relative;
}

nav ul li a {
    font-weight: 600;
    transition: var(--transition);
    padding: 5px 0;
    font-size: 16px;
    position: relative;
}

nav ul li a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    background-color: var(--secondary-color);
    bottom: -2px;
    left: 0;
    transition: var(--transition);
}

nav ul li a:hover::after {
    width: 100%;
}

nav ul li a:hover {
    color: var(--secondary-color);
}

header.scrolled nav ul li a:hover {
    color: var(--primary-color);
}

.contact-btn {
    padding: 8px 20px;
    background-color: var(--secondary-color);
    color: white !important;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.contact-btn:hover {
    background-color: var(--primary-color);
    transform: translateY(-2px);
}

header.scrolled .contact-btn {
    background-color: var(--primary-color);
}

header.scrolled .contact-btn:hover {
    background-color: white;
    color: var(--primary-color) !important;
}

.mobile-menu-btn {
    display: none;
    cursor: pointer;
    font-size: 24px;
    color: var(--primary-color);
}

/* Hero Section */
.hero {
    background: linear-gradient(rgba(20, 33, 61, 0.85), rgba(20, 33, 61, 0.85)), url("../img/fondo_estudio.jpg") center/cover no-repeat;
    height: 100vh;
    display: flex;
    align-items: center;
    color: #fff;
    text-align: center;
    padding-top: 80px;
    position: relative;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(20, 33, 61, 0.85), rgba(20, 33, 61, 0.75));
}

.hero-content {
    max-width: 700px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.hero-logo {
    max-width: 550px;
    animation: fadeIn 1.5s ease;
}

.hero h1 {
    font-size: 48px;
    margin-bottom: 25px;
    font-weight: 400;
    color: #fff;
    animation: fadeInUp 1s ease 0.5s both;
    line-height: 1.2;
}

.hero h1 strong {
    font-weight: 700;
}

.hero p {
    font-size: 20px;
    margin-bottom: 35px;
    opacity: 0.9;
    animation: fadeInUp 1s ease 0.8s both;
    line-height: 1.6;
}

.btn {
    display: inline-block;
    padding: 14px 34px;
    background-color: var(--secondary-color);
    color: #fff;
    font-weight: 600;
    border-radius: var(--border-radius);
    transition: var(--transition);
    text-transform: uppercase;
    letter-spacing: 1px;
    animation: fadeInUp 1s ease 1.1s both;
    border: 2px solid var(--secondary-color);
}

.btn:hover {
    background-color: transparent;
    color: var(--secondary-color);
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* About Section */
.about {
    padding: 100px 0;
    background-color: #fff;
    position: relative;
    overflow: hidden;
}

.about::before {
    content: '';
    position: absolute;
    width: 300px;
    height: 300px;
    background-color: rgba(252, 163, 17, 0.05);
    border-radius: 50%;
    top: -150px;
    left: -150px;
    z-index: 0;
}

.section-header {
    text-align: center;
    margin-bottom: 70px;
    position: relative;
}

.section-subtitle {
    display: block;
    color: var(--secondary-color);
    font-size: 18px;
    font-style: italic;
    margin-bottom: 15px;
    font-family: var(--heading-font);
}

.section-header h2 {
    font-size: 40px;
    color: var(--primary-color);
    margin-bottom: 20px;
    position: relative;
    display: inline-block;
}

.section-header h2::after {
    content: '';
    position: absolute;
    width: 70px;
    height: 3px;
    background-color: var(--secondary-color);
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
}

.section-header p {
    max-width: 700px;
    margin: 20px auto 0;
    color: #666;
}

.about-content {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 1;
}

.about-img {
    flex: 1;
    position: relative;
    padding-right: 30px;
}

.about-img img {
    border-radius: 6px;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.about-img:hover img {
    transform: scale(1.02);
}

.experience-badge {
    position: absolute;
    right: 0;
    bottom: 30px;
    background-color: var(--secondary-color);
    color: white;
    padding: 20px;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 120px;
    height: 120px;
    box-shadow: var(--shadow);
    transform: translateX(40%);
    text-align: center;
}

.years {
    font-size: 36px;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 5px;
}

.text {
    font-size: 14px;
    font-weight: 500;
}

.about-text {
    flex: 1;
    padding-left: 50px;
}

.about-text h3 {
    font-size: 28px;
    margin-bottom: 25px;
    color: var(--primary-color);
    position: relative;
    padding-bottom: 15px;
}

.about-text h3::after {
    content: '';
    position: absolute;
    width: 50px;
    height: 3px;
    background-color: var(--secondary-color);
    bottom: 0;
    left: 0;
}

.highlighted {
    font-size: 18px;
    color: var(--primary-color);
    margin-bottom: 20px;
    font-style: italic;
    border-left: 3px solid var(--secondary-color);
    padding-left: 15px;
}

.about-text p {
    margin-bottom: 20px;
    color: #666;
    font-size: large;
}

.about-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.feature {
    display: flex;
    align-items: center;
    gap: 15px;
}

.feature i {
    color: var(--secondary-color);
    font-size: 24px;
}

.feature span {
    font-weight: 600;
    font-size: 16px;
}

/* Services Section */
.services {
    padding: 100px 0;
    background-color: #f9fafb;
    position: relative;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.service-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: var(--transition);
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    border: none;
    opacity: 0;
    transform: translateY(20px);
}

.service-card.appear {
    opacity: 1;
    transform: translateY(0);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
    transition: var(--transition);
}

.service-icon {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin: 30px auto 20px;
    font-size: 28px;
    position: relative;
    z-index: 1;
    color: var(--secondary-color);
    background-color: rgba(252, 163, 17, 0.1);
    transition: var(--transition);
}

.service-card:hover .service-icon {
    transform: rotateY(180deg);
    color: white;
    background-color: var(--secondary-color);
}

.service-content {
    padding: 0 30px 30px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.service-title {
    font-size: 22px;
    color: var(--primary-color);
    margin-bottom: 20px;
    text-align: center;
    position: relative;
    padding-bottom: 15px;
}

.service-title::after {
    content: '';
    position: absolute;
    width: 40px;
    height: 2px;
    background-color: var(--secondary-color);
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
}

.service-description {
    color: #666;
    margin-bottom: 25px;
    flex-grow: 1;
    line-height: 1.7;
}

.service-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    color: var(--secondary-color);
    font-weight: 600;
    padding: 5px 0;
    transition: var(--transition);
    align-self: flex-start;
    margin-top: auto;
}

.service-link i {
    transition: var(--transition);
}

.service-link:hover {
    color: var(--primary-color);
}

.service-link:hover i {
    transform: translateX(5px);
}

/* Team Section */
.team {
    padding: 100px 0;
    background-color: #fff;
    position: relative;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    position: relative;
    z-index: 1;
}

.team-member {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    transition: all 0.4s ease;
}

.team-member:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.member-img {
    width: 100%;
    height: 350px;
    overflow: hidden;
    position: relative;
}

.member-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.5s ease;
}

.team-member:hover .member-img img {
    transform: scale(1.1);
}

.member-overlay {
    position: absolute;
    bottom: -60px;
    left: 0;
    width: 100%;
    height: 60px;
    background-color: rgba(20, 33, 61, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.4s ease;
}

.team-member:hover .member-overlay {
    bottom: 0;
}

.social-icons {
    display: flex;
    gap: 15px;
}

.social-link {
    width: 38px;
    height: 38px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: var(--secondary-color);
    transform: translateY(-3px);
}

.member-info {
    padding: 25px;
    text-align: center;
}

.member-name {
    font-size: 22px;
    color: var(--primary-color);
    margin-bottom: 8px;
    font-weight: 600;
}

.member-role {
    color: var(--secondary-color);
    font-size: 16px;
    letter-spacing: 1px;
    margin-bottom: 15px;
    font-weight: 500;
    text-transform: uppercase;
}

.member-bio {
    color: #666;
    font-size: 15px;
    line-height: 1.8;
}

/* Contact Section */
.contact {
    padding: 100px 0;
    background: linear-gradient(rgba(20, 33, 61, 0.95), rgba(20, 33, 61, 0.95)), url('/api/placeholder/1200/600') center/cover no-repeat;
    color: #fff;
    position: relative;
}

.contact h2 {
    color: #fff;
}

.section-header.light p {
    color: rgba(255, 255, 255, 0.8);
}

.contact-wrapper {
    display: grid;
    grid-template-columns: 1fr 1.5fr;
    gap: 50px;
    margin-top: 30px;
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
}

.contact-info-column {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.contact-info {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.contact-item {
    display: flex;
    margin-bottom: 25px;
    align-items: flex-start;
}

.contact-item:last-child {
    margin-bottom: 0;
}

.contact-item i {
    font-size: 22px;
    color: var(--secondary-color);
    margin-right: 20px;
    min-width: 22px;
    margin-top: 3px;
}

.contact-item div h3 {
    font-size: 18px;
    margin-bottom: 6px;
    color: #fff;
}

.contact-item div p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    font-size: 15px;
    word-break: break-word;
}

.map-container {
    width: 100%;
    height: 300px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    border: 5px solid rgba(255, 255, 255, 0.1);
}

.contact-form-column {
    position: relative;
}

.contact-form {
    background-color: #fff;
    padding: 35px;
    border-radius: 8px;
    height: 100%;
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.2);
    position: relative;
    max-width: 100%;
}

.contact-form::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
}

.contact-form h3 {
    font-size: 26px;
    margin-bottom: 15px;
    color: var(--primary-color);
}

.contact-form p {
    color: #666;
    margin-bottom: 30px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 0;
}

.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    color: var(--primary-color);
    font-size: 15px;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    font-size: 15px;
    transition: all 0.3s ease;
    font-family: var(--body-font);
    box-sizing: border-box;
}

.form-control:focus {
    border-color: var(--secondary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(252, 163, 17, 0.2);
}

textarea.form-control {
    height: 120px;
    resize: none;
}

.btn-contact {
    display: inline-block;
    padding: 12px 28px;
    background-color: var(--secondary-color);
    color: #fff;
    font-weight: 600;
    border-radius: 4px;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-align: center;
    width: auto;
    margin: 10px auto 0;
    display: block;
    font-size: 15px;
}

.btn-contact:hover {
    background-color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Footer */
footer {
    background-color: var(--primary-color);
    color: #fff;
    padding: 70px 0 20px;
    position: relative;
}

.footer-content {
    display: grid;
    grid-template-columns: 1.5fr repeat(3, 1fr);
    gap: 40px;
    margin-bottom: 40px;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.footer-section {
    padding-right: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.footer-logo {
    margin-bottom: 20px;
    max-width: 180px;
}

.footer-logo img {
    width: 100%;
    height: auto;
    display: block;
    margin: 0 auto;
}

.about-footer p {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 20px;
    line-height: 1.7;
    font-size: 15px;
    text-align: center;
    max-width: 90%;
    margin-left: auto;
    margin-right: auto;
}

.social-footer {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    justify-content: center;
}

.social-icon {
    width: 38px;
    height: 38px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    transition: all 0.3s ease;
    font-size: 15px;
}

.social-icon:hover {
    background-color: var(--secondary-color);
    transform: translateY(-3px);
}

.footer-section h3 {
    font-size: 18px;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 12px;
    color: #fff;
    text-align: center;
}

.footer-section h3::after {
    content: '';
    position: absolute;
    left: 50%;
    bottom: 0;
    width: 30px;
    height: 2px;
    background-color: var(--secondary-color);
    transform: translateX(-50%);
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
    text-align: center;
    width: 100%;
    display: inline-block;
}

.footer-links li {
    margin-bottom: 10px;
    position: relative;
    display: inline-block;
    text-align: center;
    width: 100%;
}

.footer-links li a {
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.3s ease;
    display: inline-block;
    position: relative;
    font-size: 15px;
    padding: 0;
}

.footer-links li a:hover {
    color: var(--secondary-color);
    transform: translateY(-3px);
}

.footer-contact-info {
    list-style: none;
    padding: 0;
    margin: 0;
    width: 100%;
    text-align: center;
}

.footer-contact-info li {
    display: flex;
    gap: 15px;
    margin-bottom: 12px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 15px;
    align-items: flex-start;
    justify-content: flex-start;
    text-align: left;
    max-width: 280px;
    margin-left: auto;
    margin-right: auto;
}

.footer-contact-info li i {
    color: var(--secondary-color);
    min-width: 16px;
    margin-top: 4px;
    font-size: 16px;
    display: block;
}

.footer-contact-info li a {
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.3s ease;
    text-align: left;
    display: inline-block;
    white-space: nowrap;
}

.footer-contact-info li a:hover {
    color: var(--secondary-color);
}

.footer-contact-info li div {
    text-align: left;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Específico para email e Instagram para evitar cortes */
.footer-contact-info li:has(.fas.fa-envelope) div,
.footer-contact-info li:has(.fab.fa-instagram) div {
    white-space: nowrap;
    overflow: visible;
    text-overflow: unset;
    font-size: 14px;
}

/* Fallback para navegadores que no soportan :has() */
.footer-contact-info li:nth-child(3) div,
.footer-contact-info li:nth-child(4) div {
    white-space: nowrap;
    overflow: visible;
    text-overflow: unset;
    font-size: 14px;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Styles */
@media (max-width: 1700px) {
    .logo img {
        width: 10vw;
        height: 7vh;
        object-fit: contain;
    }
}

@media (max-width: 1200px) {
    .container {
        max-width: 95%;
        padding: 0 15px;
    }

    .logo img {
        width: 280px;
        height: 150px;
    }

    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: 40px;
        row-gap: 30px;
    }

    .services-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 25px;
    }

    nav ul li {
        margin-left: 25px;
    }

    .hero h1 {
        font-size: 42px;
    }

    .hero p {
        font-size: 18px;
    }

    .contact-wrapper {
        grid-template-columns: 1fr 1.2fr;
        gap: 30px;
    }
}

@media (max-width: 992px) {
    .team-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }

    .logo img {
        width: 250px;
        height: 120px;
    }

    .about-content {
        flex-direction: column;
    }

    .about-text {
        padding-left: 0;
        padding-top: 40px;
    }

    .col {
        flex: 0 0 50%;
        margin-bottom: 30px;
    }

    .contact-wrapper {
        grid-template-columns: 1fr;
        gap: 40px;
        max-width: 700px;
    }

    .experience-badge {
        right: auto;
        left: 30px;
        transform: translateX(0);
    }

    .section-header h2 {
        font-size: 36px;
    }

    .contact-info-column, .contact-form-column {
        width: 100%;
    }

    .contact-info {
        max-width: 100%;
    }

    .map-container {
        height: 300px;
        margin-top: 20px;
    }

    .contact-form {
        padding: 30px;
        max-width: 100%;
    }

    .about {
        padding: 80px 0;
    }

    .services {
        padding: 80px 0;
    }

    .footer-content {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }

    .footer-section {
        padding-right: 0;
    }

    .footer-logo {
        max-width: 160px;
    }

    /* Ajustes para email e Instagram en tablets */
    .footer-contact-info li {
        max-width: 250px;
    }

    .footer-contact-info li:has(.fas.fa-envelope) div,
    .footer-contact-info li:has(.fab.fa-instagram) div,
    .footer-contact-info li:nth-child(3) div,
    .footer-contact-info li:nth-child(4) div {
        font-size: 14px;
        white-space: nowrap;
    }
}

@media (max-width: 768px) {
    .team-grid {
        grid-template-columns: 1fr;
        max-width: 450px;
        margin: 0 auto;
    }

    .mobile-menu-btn {
        display: block;
        z-index: 1001;
    }

    .services-grid {
        grid-template-columns: 1fr;
        max-width: 450px;
        margin: 0 auto;
    }

    nav {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(20, 33, 61, 0.95);
        padding: 80px 20px 20px;
        box-shadow: none;
        display: none;
        border-radius: 0;
        z-index: 1000;
        overflow-y: auto;
    }

    header.scrolled nav {
        background-color: rgba(252, 163, 17, 0.95);
    }

    nav.active {
        display: block;
        animation: fadeIn 0.3s ease;
    }

    nav ul {
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
    }

    nav ul li {
        margin: 15px 0;
        width: 100%;
        text-align: center;
    }

    nav ul li a {
        display: block;
        padding: 12px 0;
        font-size: 18px;
        color: white;
    }

    nav ul li a::after {
        background-color: white;
    }

    .contact-btn {
        background-color: white;
        color: var(--primary-color) !important;
        margin-top: 15px;
    }

    .contact-btn:hover {
        background-color: var(--secondary-color);
        color: white !important;
    }

    .hero h1 {
        font-size: 32px;
    }

    .hero p {
        font-size: 16px;
        max-width: 90%;
        margin-left: auto;
        margin-right: auto;
    }

    .col {
        flex: 0 0 100%;
    }

    .section-header h2 {
        font-size: 30px;
    }

    .section-header p {
        font-size: 16px;
        max-width: 90%;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .hero-logo {
        max-width: 220px;
    }

    .about, .services, .team, .contact {
        padding: 60px 0;
    }

    .section-header {
        margin-bottom: 50px;
    }

    .about-text h3 {
        font-size: 24px;
    }

    .highlighted {
        font-size: 16px;
    }

    .about-text p {
        font-size: 16px;
    }

    .contact-info {
        padding: 25px;
    }

    .contact-form {
        padding: 25px;
    }
}

@media (max-width: 576px) {
    .logo img {
        width: 220px;
        height: 130px;
    }

    .about-features {
        grid-template-columns: 1fr;
    }

    .service-card {
        margin: 0 auto;
        max-width: 100%;
    }

    .footer-logo img {
        margin: 0 auto;
        display: block;
        max-width: 140px;
    }

    .social-footer {
        justify-content: center;
    }

    .hero h1 {
        font-size: 28px;
    }

    .hero p {
        font-size: 15px;
    }

    .btn {
        padding: 12px 28px;
        font-size: 14px;
    }

    .section-header h2 {
        font-size: 26px;
    }

    .section-subtitle {
        font-size: 16px;
    }

    .contact-item {
        flex-direction: row;
        text-align: left;
        align-items: flex-start;
    }

    .contact-item i {
        margin-bottom: 0;
        margin-right: 15px;
        margin-top: 3px;
    }

    .contact-info {
        padding: 20px;
    }

    .contact-form {
        padding: 25px 20px;
    }

    .map-container {
        height: 250px;
    }

    .whatsapp-float {
        width: 50px;
        height: 50px;
        bottom: 20px;
        right: 20px;
        font-size: 24px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .footer-section {
        max-width: 100%;
        padding: 0 15px;
    }

    .footer-section h3::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .footer-links li a {
        padding: 0;
    }

    .footer-contact-info li {
        justify-content: flex-start;
        max-width: 300px;
        margin-left: auto;
        margin-right: auto;
    }

    .footer-contact-info li div {
        text-align: left;
        flex: 1;
    }

    /* Mantener email e Instagram en una línea en móviles */
    .footer-contact-info li:has(.fas.fa-envelope) div,
    .footer-contact-info li:has(.fab.fa-instagram) div,
    .footer-contact-info li:nth-child(3) div,
    .footer-contact-info li:nth-child(4) div {
        font-size: 13px;
        white-space: nowrap;
    }

    .footer-contact-info li i {
        margin-top: 3px;
    }

    .about-footer p {
        max-width: 100%;
    }

    footer {
        padding: 50px 0 20px;
    }
}

@media (max-width: 400px) {
    .header-content {
        padding: 5px 0;
    }

    .logo img {
        width: 200px;
        height: 75px;
    }

    .hero h1 {
        font-size: 24px;
    }

    .hero p {
        font-size: 14px;
    }

    .section-header h2 {
        font-size: 24px;
    }

    .btn {
        padding: 10px 24px;
        font-size: 13px;
    }

    .contact-form h3 {
        font-size: 20px;
    }

    .form-control {
        padding: 10px;
    }

    .contact-info {
        padding: 15px;
    }

    .contact-form {
        padding: 20px 15px;
    }

    .contact-item i {
        font-size: 18px;
    }

    .footer-section h3 {
        font-size: 16px;
    }

    .footer-bottom p {
        font-size: 13px;
    }

    .footer-links li a,
    .footer-contact-info li,
    .about-footer p {
        font-size: 14px;
    }

    /* Ajustes específicos para email e Instagram en pantallas muy pequeñas */
    .footer-contact-info li:has(.fas.fa-envelope) div,
    .footer-contact-info li:has(.fab.fa-instagram) div,
    .footer-contact-info li:nth-child(3) div,
    .footer-contact-info li:nth-child(4) div {
        font-size: 12px;
        white-space: nowrap;
    }

    .footer-contact-info li {
        max-width: 280px;
    }
}

.whatsapp-float {
    position: fixed;
    width: 60px;
    height: 60px;
    bottom: 40px;
    right: 40px;
    background-color: #25d366;
    color: #FFF;
    border-radius: 50px;
    text-align: center;
    font-size: 30px;
    box-shadow: 2px 2px 3px #999;
    z-index: 100;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.whatsapp-float:hover {
    background-color: #128C7E;
    transform: scale(1.1);
}

/* Responsive Logo Styles */
@media (max-width: 768px) {
    .logo img {
        height: 50px;
    }

    .header-content {
        padding: 10px 0;
    }
}

@media (max-width: 480px) {
    .logo img {
        height: 45px;
    }

    .header-content {
        padding: 8px 0;
    }
}