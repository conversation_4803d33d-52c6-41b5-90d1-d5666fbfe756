# Configuración de Variables de Entorno

## Para Desarrollo Local

1. Copia `static/scripts/config.example.js` a `static/scripts/config.js`
2. Edita `config.js` con tus credenciales reales
3. El archivo `config.js` está en `.gitignore` y no se subirá al repositorio

## Para Producción (Azure Static Web Apps)

### Configurar Variables de Entorno en Azure:

1. Ve al Azure Portal (portal.azure.com)
2. Busca tu Static Web App
3. Ve a "Configuration" → "Application settings"
4. Añade estas variables:

```
EMAILJS_SERVICE_ID = tu_service_id_real
EMAILJS_TEMPLATE_ID = tu_template_id_real
EMAILJS_USER_ID = tu_user_id_real
GOOGLE_MAPS_API_KEY = tu_api_key_real
WHATSAPP_NUMBER = tu_numero_real
```

### Configurar Secrets en GitHub:

1. Ve a tu repositorio en GitHub
2. Settings → Secrets and variables → Actions
3. Añade los mismos secrets con los mismos nombres

## Cómo Funciona

- **Local**: Usa `config.js` con tus valores reales
- **Producción**: El script `build.js` genera un nuevo `config.js` usando las variables de entorno
- **Seguridad**: Las credenciales nunca se exponen en el código público
