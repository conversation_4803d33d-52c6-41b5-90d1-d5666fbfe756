// JavaScript para las páginas de servicios

document.addEventListener('DOMContentLoaded', function() {
    // Inicializar el manejo de las preguntas frecuentes
    initFAQAccordion();

    // Inicializar animaciones
    initAnimations();

    // Añadir clases de animación a los elementos de sección
    addSectionAnimations();

    // Inicializar eventos de resize para manejar cambios de tamaño de pantalla
    initResizeEvents();
});

// Función para manejar el acordeón de preguntas frecuentes
function initFAQAccordion() {
    const faqQuestions = document.querySelectorAll('.faq-question');

    faqQuestions.forEach(question => {
        // Ocultar todas las respuestas al inicio
        const answer = question.nextElementSibling;
        answer.style.display = 'none';
        answer.style.transition = 'max-height 0.3s ease';

        // Agregar evento de clic a cada pregunta
        question.addEventListener('click', function() {
            // Obtener el ícono dentro de la pregunta
            const icon = this.querySelector('i');

            // Cerrar todas las otras respuestas
            document.querySelectorAll('.faq-answer').forEach(item => {
                if (item !== answer && item.style.display === 'block') {
                    // Ocultar con animación
                    item.style.maxHeight = '0';

                    // Usar setTimeout para esperar a que termine la animación
                    setTimeout(() => {
                        item.style.display = 'none';
                    }, 300);

                    // Cambiar el ícono
                    const otherIcon = item.previousElementSibling.querySelector('i');
                    otherIcon.classList.remove('fa-chevron-up');
                    otherIcon.classList.add('fa-chevron-down');
                }
            });

            // Alternar la visibilidad de la respuesta actual
            if (answer.style.display === 'none') {
                // Mostrar con animación
                answer.style.display = 'block';
                answer.style.maxHeight = '0';

                // Usar setTimeout para permitir que el navegador aplique el estilo inicial
                setTimeout(() => {
                    // Calcular la altura máxima basada en el contenido
                    const maxHeight = answer.scrollHeight + 'px';
                    answer.style.maxHeight = maxHeight;
                }, 10);

                // Cambiar el ícono
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-up');
            } else {
                // Ocultar con animación
                answer.style.maxHeight = '0';

                // Usar setTimeout para esperar a que termine la animación
                setTimeout(() => {
                    answer.style.display = 'none';
                }, 300);

                // Cambiar el ícono
                icon.classList.remove('fa-chevron-up');
                icon.classList.add('fa-chevron-down');
            }
        });
    });

    // Abrir la primera pregunta por defecto en dispositivos de escritorio
    if (window.innerWidth >= 992 && faqQuestions.length > 0) {
        setTimeout(() => {
            faqQuestions[0].click();
        }, 500);
    }
}

// Función para añadir clases de animación a los elementos de sección
function addSectionAnimations() {
    // Añadir clases de animación a los encabezados de sección
    document.querySelectorAll('.section-header').forEach(header => {
        header.classList.add('animate-fade-in');
    });

    // Añadir clases de animación a los elementos de contenido
    document.querySelectorAll('.service-text p:first-of-type').forEach(p => {
        p.classList.add('animate-fade-up');
    });
}

// Función para animar elementos cuando entran en el viewport
function initAnimations() {
    const animatedElements = [
        { selector: '.benefit-card', delay: 100 },
        { selector: '.testimonial-card', delay: 150 },
        { selector: '.step', delay: 100 },
        { selector: '.faq-item', delay: 100 },
        { selector: '.service-features li', delay: 50 }
    ];

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                const index = Array.from(element.parentNode.children).indexOf(element);
                const delay = element.dataset.delay || 0;

                // Reducir los retrasos en dispositivos móviles para una experiencia más rápida
                const actualDelay = window.innerWidth <= 768 ? Math.min(50, delay) : delay;

                setTimeout(() => {
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * actualDelay);

                observer.unobserve(element);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    // Configurar cada grupo de elementos
    animatedElements.forEach(group => {
        const elements = document.querySelectorAll(group.selector);
        elements.forEach(element => {
            // Establecer estilos iniciales
            element.style.opacity = '0';
            element.style.transform = 'translateY(20px)';
            element.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            element.dataset.delay = group.delay;

            // Observar el elemento
            observer.observe(element);
        });
    });
}

// Función para manejar eventos de resize
function initResizeEvents() {
    // Ajustar alturas de elementos en resize
    window.addEventListener('resize', function() {
        // Recalcular alturas de respuestas FAQ abiertas
        document.querySelectorAll('.faq-answer').forEach(answer => {
            if (answer.style.display === 'block') {
                answer.style.maxHeight = answer.scrollHeight + 'px';
            }
        });

        // Ajustar alturas de tarjetas de testimonios para que sean uniformes
        equalizeTestimonialHeights();
    });

    // Inicializar alturas iguales para testimonios
    equalizeTestimonialHeights();
}

// Función para igualar alturas de tarjetas de testimonios
function equalizeTestimonialHeights() {
    // Solo aplicar en pantallas grandes
    if (window.innerWidth >= 768) {
        const testimonialCards = document.querySelectorAll('.testimonial-card');

        // Resetear alturas
        testimonialCards.forEach(card => {
            card.style.height = 'auto';
        });

        // Encontrar la altura máxima
        let maxHeight = 0;
        testimonialCards.forEach(card => {
            maxHeight = Math.max(maxHeight, card.offsetHeight);
        });

        // Aplicar altura máxima a todas las tarjetas
        if (maxHeight > 0) {
            testimonialCards.forEach(card => {
                card.style.height = maxHeight + 'px';
            });
        }
    } else {
        // En móviles, resetear alturas
        document.querySelectorAll('.testimonial-card').forEach(card => {
            card.style.height = 'auto';
        });
    }
}

// Inicializar efectos adicionales cuando la página esté completamente cargada
window.addEventListener('load', function() {
    // Añadir efecto de parallax al hero (solo en dispositivos no móviles)
    if (window.innerWidth > 768) {
        window.addEventListener('scroll', function() {
            const scrollPosition = window.scrollY;
            const heroSection = document.querySelector('.service-hero');

            if (heroSection) {
                const offset = scrollPosition * 0.4;
                heroSection.style.backgroundPositionY = `calc(50% + ${offset}px)`;
            }
        });
    }

    // Inicializar alturas iguales para testimonios
    equalizeTestimonialHeights();
});
