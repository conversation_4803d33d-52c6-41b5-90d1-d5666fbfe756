// Import configuration
import config from './config.js';

// Initialize EmailJS
(function() {
    emailjs.init(config.emailjs.userId);
})();

// Mobile menu functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const nav = document.querySelector('#main-nav');
    const body = document.body;

    if (mobileMenuBtn) {
        mobileMenuBtn.addEventListener('click', function() {
            nav.classList.toggle('active');
            this.querySelector('i').classList.toggle('fa-bars');
            this.querySelector('i').classList.toggle('fa-times');

            // Prevent body scrolling when menu is open
            if (nav.classList.contains('active')) {
                body.style.overflow = 'hidden';
            } else {
                body.style.overflow = '';
            }
        });
    }

    // Close mobile menu when clicking outside
    document.addEventListener('click', function(e) {
        if (!nav.contains(e.target) && !mobileMenuBtn.contains(e.target) && nav.classList.contains('active')) {
            nav.classList.remove('active');
            mobileMenuBtn.querySelector('i').classList.remove('fa-times');
            mobileMenuBtn.querySelector('i').classList.add('fa-bars');
            body.style.overflow = '';
        }
    });

    // Close mobile menu when clicking on a nav link
    const navLinks = nav.querySelectorAll('a');
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            if (window.innerWidth <= 768) {
                nav.classList.remove('active');
                mobileMenuBtn.querySelector('i').classList.remove('fa-times');
                mobileMenuBtn.querySelector('i').classList.add('fa-bars');
                body.style.overflow = '';
            }
        });
    });

    // Handle resize events to reset menu state
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768 && nav.classList.contains('active')) {
            nav.classList.remove('active');
            mobileMenuBtn.querySelector('i').classList.remove('fa-times');
            mobileMenuBtn.querySelector('i').classList.add('fa-bars');
            body.style.overflow = '';
        }
    });
});

// Header scroll effect
window.addEventListener('scroll', function() {
    const header = document.querySelector('header');
    const headerLogo = document.getElementById('headerLogo');

    if (window.scrollY > 100) {
        header.classList.add('scrolled');
        // Cambiar a logo azul cuando se hace scroll
        if (headerLogo) {
            headerLogo.src = 'static/img/logos/header_logo_azul.png';
        }
    } else {
        header.classList.remove('scrolled');
        // Volver al logo dorado cuando está en la parte superior
        if (headerLogo) {
            headerLogo.src = 'static/img/logos/header_logo_dorado.png';
        }
    }
});

// Contact form handling
const contactForm = document.getElementById('contactForm');
if (contactForm) {
    contactForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Get form data
        const formData = {
            from_name: document.getElementById('name').value,
            from_email: document.getElementById('email').value,
            from_phone: document.getElementById('phone').value,
            service: document.getElementById('service').options[document.getElementById('service').selectedIndex].text,
            message: document.getElementById('message').value
        };

        // Show loading state
        const submitBtn = contactForm.querySelector('button[type="submit"]');
        const originalBtnText = submitBtn.textContent;
        submitBtn.textContent = 'Enviando...';
        submitBtn.disabled = true;

        // Send email using EmailJS
        emailjs.send(config.emailjs.serviceId, config.emailjs.templateId, formData)
            .then(function() {
                // Show success message
                alert('¡Mensaje enviado con éxito! Nos pondremos en contacto contigo pronto.');
                contactForm.reset();
            })
            .catch(function(error) {
                // Show error message
                console.error('Error sending email:', error);
                alert('Hubo un error al enviar el mensaje. Por favor, intenta nuevamente.');
            })
            .finally(function() {
                // Reset button state
                submitBtn.textContent = originalBtnText;
                submitBtn.disabled = false;
            });
    });
}

// Update WhatsApp link with configured number
const whatsappLink = document.querySelector('.whatsapp-float');
if (whatsappLink) {
    const whatsappNumber = config.whatsapp.number;
    whatsappLink.href = `https://wa.me/${whatsappNumber}?text=Me%20gustaría%20consultar%20sobre%20sus%20servicios%20legales`;
}

// Service cards animation
const observerOptions = {
    threshold: 0.1
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('appear');
        }
    });
}, observerOptions);

document.querySelectorAll('.service-card').forEach(card => {
    observer.observe(card);
});

// Smooth Scrolling for Anchor Links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function(e) {
        e.preventDefault();

        document.querySelector(this.getAttribute('href')).scrollIntoView({
            behavior: 'smooth'
        });

        // Close mobile menu if open
        document.getElementById('main-nav').classList.remove('active');
        document.querySelector('.mobile-menu-btn i').classList.remove('fa-times');
        document.querySelector('.mobile-menu-btn i').classList.add('fa-bars');
    });
});