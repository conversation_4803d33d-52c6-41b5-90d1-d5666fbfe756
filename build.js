#!/usr/bin/env node

// Build script to create production config with environment variables
const fs = require('fs');
const path = require('path');

console.log('🔧 Building production configuration...');

// Create production config content with environment variables
const configContent = `// Production configuration - generated automatically
const config = {
    emailjs: {
        serviceId: '${process.env.EMAILJS_SERVICE_ID || 'service_di7tpub'}',
        templateId: '${process.env.EMAILJS_TEMPLATE_ID || 'template_9qtnasy'}',
        userId: '${process.env.EMAILJS_USER_ID || 'jGEh-G0MHuzOOJiJa'}'
    },
    googleMaps: {
        apiKey: '${process.env.GOOGLE_MAPS_API_KEY || 'YOUR_GOOGLE_MAPS_API_KEY'}'
    },
    whatsapp: {
        number: '${process.env.WHATSAPP_NUMBER || '5492991234567'}'
    }
};

// Export the config object
export default config;`;

// Write the production config file
const configPath = path.join(__dirname, 'static', 'scripts', 'config.js');
fs.writeFileSync(configPath, configContent);

console.log('✅ Production config.js generated with environment variables!');
console.log('📋 Environment variables used:');
console.log(`   EMAILJS_SERVICE_ID: ${process.env.EMAILJS_SERVICE_ID || 'default'}`);
console.log(`   EMAILJS_TEMPLATE_ID: ${process.env.EMAILJS_TEMPLATE_ID || 'default'}`);
console.log(`   EMAILJS_USER_ID: ${process.env.EMAILJS_USER_ID || 'default'}`);
console.log(`   GOOGLE_MAPS_API_KEY: ${process.env.GOOGLE_MAPS_API_KEY ? '***' : 'default'}`);
console.log(`   WHATSAPP_NUMBER: ${process.env.WHATSAPP_NUMBER || 'default'}`);
