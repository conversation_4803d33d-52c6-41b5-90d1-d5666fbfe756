/* Service Page Specific Styles */
:root {
    --primary-color: #14213d;
    --secondary-color: #fca311;
    --text-color: #333;
    --light-color: #f8f9fa;
    --dark-color: #0d1b2a;
    --gray-color: #e5e5e5;
}

/* Hero Section */
.service-hero {
    background-image: url('../img/service-bg.jpg');
    background-size: cover;
    background-position: center;
    height: 50vh;
    min-height: 400px;
    position: relative;
}

.service-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(20, 33, 61, 0.85) 0%, rgba(20, 33, 61, 0.7) 100%);
    z-index: 1;
}

.service-hero .container {
    position: relative;
    z-index: 2;
}

.service-hero .hero-content {
    text-align: center;
}

.service-hero h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    position: relative;
    display: inline-block;
    color: #fff;
}

.service-hero h1::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: var(--secondary-color);
}

.service-hero p {
    font-size: 1.2rem;
    max-width: 700px;
    margin: 1.5rem auto 0;
    color: rgba(255, 255, 255, 0.9);
}

/* Service Overview Section */
.service-overview {
    padding: 5rem 0;
    background-color: var(--light-color);
    position: relative;
}

.service-overview::before {
    content: '';
    position: absolute;
    width: 300px;
    height: 300px;
    background-color: rgba(252, 163, 17, 0.05);
    border-radius: 50%;
    top: -150px;
    left: -150px;
    z-index: 0;
}

.service-content {
    display: flex;
    flex-wrap: wrap;
    gap: 3rem;
    margin-top: 3rem;
    position: relative;
    z-index: 1;
}

.service-text {
    flex: 1;
    min-width: 300px;
}

.service-text p {
    margin-bottom: 1.5rem;
    line-height: 1.8;
    color: #666;
    font-size: 1.05rem;
}

.service-text p:first-of-type {
    font-size: 1.2rem;
    font-weight: 500;
    color: var(--primary-color);
    border-left: 3px solid var(--secondary-color);
    padding-left: 15px;
}

.service-features {
    list-style: none;
    padding: 0;
    margin: 2rem 0;
}

.service-features li {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    background-color: #fff;
    padding: 12px 15px;
    border-radius: 6px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-left: 3px solid var(--secondary-color);
}

.service-features li:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.service-features li i {
    color: var(--secondary-color);
    margin-right: 1rem;
    font-size: 1.2rem;
}

.service-image {
    flex: 1;
    min-width: 300px;
    position: relative;
}

.service-image::before {
    content: '';
    position: absolute;
    top: 15px;
    left: 15px;
    width: 100%;
    height: 100%;
    background-color: var(--secondary-color);
    border-radius: 8px;
    z-index: 0;
}

.service-image img {
    width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1;
}

/* Process Steps Section */
.process-steps {
    padding: 5rem 0;
    background-color: #fff;
    position: relative;
    overflow: hidden;
}

.process-steps::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(20, 33, 61, 0.03) 0%, rgba(252, 163, 17, 0.03) 100%);
    z-index: 0;
}

.process-steps .container {
    position: relative;
    z-index: 1;
}

.steps-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 2rem;
    margin-top: 3rem;
}

.step {
    flex: 1;
    min-width: 200px;
    max-width: 250px;
    text-align: center;
    padding: 2.5rem 2rem;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    position: relative;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-bottom: 3px solid var(--secondary-color);
}

.step:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.step-number {
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    background-color: var(--secondary-color);
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    box-shadow: 0 4px 10px rgba(252, 163, 17, 0.3);
}

.step-icon {
    font-size: 2.5rem;
    color: var(--secondary-color);
    margin-bottom: 1.5rem;
    transition: transform 0.3s ease;
}

.step:hover .step-icon {
    transform: scale(1.1);
}

.step h3 {
    margin-bottom: 1rem;
    font-size: 1.2rem;
    color: var(--primary-color);
    position: relative;
    padding-bottom: 10px;
}

.step h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 2px;
    background-color: var(--secondary-color);
}

.step p {
    color: #666;
    line-height: 1.6;
}

/* Why Choose Us Section */
.why-choose-us {
    padding: 5rem 0;
    background-color: var(--light-color);
    position: relative;
}

.why-choose-us::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(252, 163, 17, 0.08) 0%, rgba(20, 33, 61, 0.05) 100%);
}

.benefits-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 3rem;
}

.benefit-card {
    background-color: #fff;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.benefit-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
}

.benefit-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.benefit-icon {
    font-size: 2.5rem;
    color: var(--secondary-color);
    margin-bottom: 1.5rem;
    transition: transform 0.3s ease;
}

.benefit-card:hover .benefit-icon {
    transform: scale(1.1);
}

.benefit-title {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
    font-weight: 600;
}

.benefit-description {
    color: #666;
    line-height: 1.6;
}

/* Testimonials Section */
.testimonials {
    padding: 5rem 0;
    background-color: #fff;
    position: relative;
}

.testimonials::before {
    content: '';
    position: absolute;
    right: 0;
    bottom: 0;
    width: 200px;
    height: 200px;
    background-color: rgba(252, 163, 17, 0.05);
    border-radius: 50%;
    z-index: 0;
}

.testimonials-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-top: 3rem;
    position: relative;
    z-index: 1;
}

.testimonial-card {
    background-color: var(--light-color);
    padding: 2.5rem;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    position: relative;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-left: 4px solid var(--secondary-color);
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.testimonial-card::before {
    content: '\201C';
    font-family: Georgia, serif;
    font-size: 5rem;
    color: var(--secondary-color);
    opacity: 0.2;
    position: absolute;
    top: 10px;
    left: 20px;
}

.testimonial-text {
    font-style: italic;
    margin-bottom: 1.5rem;
    color: #555;
    line-height: 1.8;
    position: relative;
    z-index: 1;
    font-size: 1.05rem;
}

.testimonial-author {
    display: flex;
    align-items: center;
}

.testimonial-author-img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-right: 1rem;
    object-fit: cover;
    border: 3px solid var(--secondary-color);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.initials-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-right: 1rem;
    border: 3px solid var(--secondary-color);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    color: white;
    text-transform: uppercase;
    background-color: var(--primary-color);
}

.testimonial-author-info h4 {
    margin: 0;
    color: var(--primary-color);
    font-weight: 600;
}

.testimonial-author-info p {
    margin: 0;
    color: var(--secondary-color);
    font-size: 0.9rem;
    font-weight: 500;
}

/* FAQ Section */
.faq-section {
    padding: 5rem 0;
    background-color: var(--light-color);
    position: relative;
}

.faq-section::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 150px;
    height: 150px;
    background-color: rgba(20, 33, 61, 0.05);
    border-radius: 50%;
    transform: translateY(-50%);
    z-index: 0;
}

.faq-container {
    margin-top: 3rem;
    position: relative;
    z-index: 1;
    max-width: 900px;
    margin-left: auto;
    margin-right: auto;
}

.faq-item {
    margin-bottom: 1.5rem;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-left: 4px solid var(--primary-color);
}

.faq-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.faq-question {
    padding: 1.5rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    color: var(--primary-color);
    transition: background-color 0.3s ease;
}

.faq-question:hover {
    background-color: rgba(252, 163, 17, 0.05);
}

.faq-question i {
    color: var(--secondary-color);
    transition: transform 0.3s ease;
}

.faq-answer {
    padding: 0 1.5rem 1.5rem;
    color: #666;
    line-height: 1.6;
    font-size: 1.05rem;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    margin-top: -1px;
}

/* Section Header Animation */
.section-header {
    position: relative;
}

.section-header h2 {
    position: relative;
    display: inline-block;
    margin-bottom: 20px;
}

.section-header h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 70px;
    height: 3px;
    background-color: var(--secondary-color);
}

.section-subtitle {
    color: var(--secondary-color);
    font-size: 18px;
    font-style: italic;
    margin-bottom: 15px;
    display: block;
}

/* Animation Classes */
.animate-fade-in {
    animation: fadeIn 1s ease;
}

.animate-fade-up {
    animation: fadeInUp 1s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
    .container {
        max-width: 95%;
        padding: 0 15px;
    }

    .service-hero h1 {
        font-size: 3rem;
    }

    .benefits-container {
        gap: 1.5rem;
    }

    .testimonials-container {
        gap: 1.5rem;
    }
}

@media (max-width: 992px) {
    .service-hero h1 {
        font-size: 2.8rem;
    }

    .service-content {
        flex-direction: column;
    }

    .service-image {
        order: -1;
        margin-bottom: 2rem;
    }

    .service-image::before {
        top: 10px;
        left: 10px;
    }

    .benefits-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .testimonials-container {
        grid-template-columns: 1fr;
    }

    .faq-container {
        max-width: 100%;
    }

    .section-header h2 {
        font-size: 2.2rem;
    }

    .section-header p {
        max-width: 90%;
        margin-left: auto;
        margin-right: auto;
    }
}

@media (max-width: 768px) {
    .service-hero {
        height: 40vh;
        min-height: 300px;
    }

    .service-hero h1 {
        font-size: 2.2rem;
    }

    .service-hero p {
        font-size: 1rem;
        max-width: 90%;
    }

    .steps-container {
        flex-direction: column;
        align-items: center;
    }

    .step {
        width: 100%;
        max-width: 100%;
        margin-bottom: 2rem;
    }

    .benefits-container {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .benefit-card {
        padding: 1.5rem;
    }

    .testimonial-card {
        padding: 1.5rem;
    }

    .testimonial-text {
        font-size: 1rem;
    }

    .faq-question {
        padding: 1.2rem;
        font-size: 1rem;
    }

    .faq-answer {
        padding: 0 1.2rem 1.2rem;
        font-size: 0.95rem;
    }

    .section-header h2 {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .service-hero h1 {
        font-size: 1.8rem;
    }

    .service-hero p {
        font-size: 0.9rem;
    }

    .service-text p {
        font-size: 0.95rem;
    }

    .service-text p:first-of-type {
        font-size: 1rem;
    }

    .service-features li {
        padding: 10px 12px;
    }

    .step {
        padding: 2rem 1.5rem;
    }

    .step-icon {
        font-size: 2rem;
    }

    .benefit-icon {
        font-size: 2rem;
    }

    .benefit-title {
        font-size: 1.1rem;
    }

    .testimonial-author-img {
        width: 50px;
        height: 50px;
    }

    .section-header h2 {
        font-size: 1.8rem;
    }

    .section-subtitle {
        font-size: 1rem;
    }

    .faq-question {
        font-size: 0.95rem;
        padding: 1rem;
    }

    .faq-answer p {
        font-size: 0.9rem;
    }
}

/* Fix for very small screens */
@media (max-width: 400px) {
    .service-hero h1 {
        font-size: 1.6rem;
    }

    .service-hero p {
        font-size: 0.85rem;
    }

    .section-header h2 {
        font-size: 1.6rem;
    }

    .section-subtitle {
        font-size: 0.9rem;
    }

    .step-number {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    .testimonial-author-info h4 {
        font-size: 0.9rem;
    }

    .testimonial-author-info p {
        font-size: 0.8rem;
    }
}
